package com.wosai.pay.common.state.manager;

import com.wosai.pay.common.state.manager.result.StateManagerResult;
import lombok.Data;

/**
 * ProviderMchIdEntity的状态查询结果类
 */
@Data
public class ProviderMchIdStateResult extends StateManagerResult {

    /**
     * 服务商ID
     */
    private Integer provider;

    /**
     * 服务商商户ID
     */
    private String providerMchId;

    public ProviderMchIdStateResult() {
        super();
    }

    public ProviderMchIdStateResult(Integer provider, String providerMchId) {
        this();
        this.provider = provider;
        this.providerMchId = providerMchId;
    }
}
