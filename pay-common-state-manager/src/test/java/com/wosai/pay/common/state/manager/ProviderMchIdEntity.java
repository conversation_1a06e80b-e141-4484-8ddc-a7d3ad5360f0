package com.wosai.pay.common.state.manager;

import com.wosai.pay.common.state.manager.request.AbstractEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class ProviderMchIdEntity extends AbstractEntity {

    public static final String OBJECT_TYPE_PROVIDER_MCH_ID = "ProviderMchIdEntity";

    private Integer provider;

    private String providerMchId;

    public ProviderMchIdEntity() {
        setObjectType(objectType());
    }

    public ProviderMchIdEntity(Integer provider, String providerMchId) {
        this();
        this.provider = provider;
        this.providerMchId = providerMchId;
    }

    public static String objectType() {
        return OBJECT_TYPE_PROVIDER_MCH_ID;
    }

    static {
        registerType(objectType(), ProviderMchIdEntity.class);
    }
}
