package com.wosai.pay.common.state.manager;


import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.wosai.pay.common.state.manager.dao.StateConfigDao;
import com.wosai.pay.common.state.manager.dao.StateDao;
import com.wosai.pay.common.state.manager.service.StateManagerConfig;
import com.wosai.pay.common.state.manager.service.StateManagerService;
import com.wosai.pay.common.state.manager.service.StateManagerServiceImpl;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;


import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * Test configuration for H2 embedded database
 */
@Configuration
public class TestConfiguration {

    @Resource
    BusinessOpLogService businessOpLogService;

    @Bean
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("**********************************************************************************");
        config.setUsername("p_tangguixin");
        config.setPassword("qujK_)ZvQCY*=FXrGcAys95pPxd7");

        config.setDriverClassName("com.mysql.cj.jdbc.Driver");

        return new HikariDataSource(config);
    }

    @Bean
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }

    @Bean
    public StateConfigDao stateConfigDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new StateConfigDao(namedParameterJdbcTemplate);
    }

    @Bean
    public StateDao StateDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new StateDao("state",namedParameterJdbcTemplate);
    }

    @Bean
    public StateManagerConfig stateManagerConfig(StateConfigDao stateDao) {
        return new StateManagerConfig(stateDao,10,true);
    }

    @Bean
    public StateManagerService stateManagerService(StateDao stateDao) {
        return new StateManagerServiceImpl(stateDao,businessOpLogService );
    }

    @Bean
    public JsonProxyFactoryBean businessOpLogService() {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl("http://business-logstash.beta.iwosai.com" + "/rpc/businessOpLog");
        jsonProxyFactoryBean.setServiceInterface(BusinessOpLogService.class);
        jsonProxyFactoryBean.setServerName("business-logstash");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);
        return jsonProxyFactoryBean;
    }

}

