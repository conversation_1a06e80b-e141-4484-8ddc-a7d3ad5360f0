package com.wosai.pay.common.state.manager;


import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.wosai.pay.common.base.util.MapUtil;
import com.wosai.pay.common.state.manager.dao.StateConfigDao;
import com.wosai.pay.common.state.manager.dao.StateDao;
import com.wosai.pay.common.state.manager.service.StateManagerConfig;
import com.wosai.pay.common.state.manager.service.StateManagerService;
import com.wosai.pay.common.state.manager.service.StateManagerServiceImpl;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;


import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * Test configuration for H2 embedded database
 */
@Configuration
public class TestConfiguration {

    @Resource
    BusinessOpLogService businessOpLogService;

    @Bean
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("**********************************************************************************");
        config.setUsername("p_tangguixin");
        config.setPassword("qujK_)ZvQCY*=FXrGcAys95pPxd7");

        config.setDriverClassName("com.mysql.cj.jdbc.Driver");

        return new HikariDataSource(config);
    }

    @Bean
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }

    @Bean
    public StateConfigDao stateConfigDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new StateConfigDao(namedParameterJdbcTemplate);
    }

    @Bean
    public StateDao StateDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new StateDao("state",namedParameterJdbcTemplate);
    }

    @Bean
    public StateManagerConfig stateManagerConfig(StateConfigDao stateDao) {
        return new StateManagerConfig(stateDao,10,true);
    }

    @Bean
    public StateManagerService stateManagerService(StateDao stateDao) {
        return new StateManagerServiceImpl(stateDao, businessOpLogService );
    }

    @Bean
    public JsonProxyFactoryBean businessOpLogService() {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl("http://business-logstash.beta.iwosai.com" + "/rpc/businessOpLog");
        jsonProxyFactoryBean.setServiceInterface(BusinessOpLogService.class);
        jsonProxyFactoryBean.setServerName("business-logstash");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);
        return jsonProxyFactoryBean;
    }

    @Bean
     public KafkaTemplate<String, Object> stateManagerKafkaTemplate() {
        Map<String, Object> configs = MapUtil.hashMap(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "aliyun-beta-kafka-01.iwosai.com:9092,aliyun-beta-kafka-02.iwosai.com:9092,aliyun-beta-kafka-03.iwosai.com:9092",
                ProducerConfig.ACKS_CONFIG, "1",
                ProducerConfig.BATCH_SIZE_CONFIG, "16384",
                ProducerConfig.LINGER_MS_CONFIG, "1000",
                ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, "30000",
                ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class,
                ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class
        );
        return new KafkaTemplate<>(new DefaultKafkaProducerFactory<>(configs));
    }

}

