package com.wosai.pay.common.state.manager;

import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.state.manager.entity.AbstractStateDO;
import com.wosai.pay.common.state.manager.registry.StateManagerProcessor;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;

/**
 * ProviderMchIdEntity的状态处理器
 */
public class ProviderMchIdProcessor implements StateManagerProcessor<ProviderMchIdEntity,ProviderMchIdDO, ProviderMchIdStateResult> {

    @Override
    public String getSupportedEntityType() {
        return "ProviderMchIdEntity";
    }

    @Override
    public Criteria generateCriteria(ProviderMchIdEntity entity) {
        Criteria criteria = new Criteria();
        if (entity != null) {
            if (entity.getProvider() != null) {
                criteria.with("provider").is(entity.getProvider());
            }
            if (entity.getProviderMchId() != null) {
                criteria.with("provider_mch_id").is(entity.getProviderMchId());
            }
        }
        return criteria;
    }

    @Override
    public ProviderMchIdDO buildStateDO(ProviderMchIdEntity entity) {
        ProviderMchIdDO providerMchIdDO = new ProviderMchIdDO();
        providerMchIdDO.setProvider(entity.getProvider());
        providerMchIdDO.setProviderMchId(entity.getProviderMchId());
        return providerMchIdDO;
    }

    @Override
    public ProviderMchIdStateResult createResultInstance(ProviderMchIdEntity entity) {
        ProviderMchIdStateResult result = new ProviderMchIdStateResult();
        result.setState(Boolean.TRUE); // 默认状态为启用

        if (entity != null) {
            result.setProvider(entity.getProvider());
            result.setProviderMchId(entity.getProviderMchId());
        }

        return result;
    }

    @Override
    public void afterStateChange(ProviderMchIdEntity entity, ProviderMchIdStateResult previousState,
                               ProviderMchIdStateResult currentState, OperationLogRequest operationLogRequest) {
        // 状态变更后的处理逻辑
        // 可以在这里添加日志记录、通知等业务逻辑
        System.out.println("ProviderMchIdEntity state changed for provider: " +
                          (entity != null ? entity.getProvider() : "null") +
                          ", mchId: " + (entity != null ? entity.getProviderMchId() : "null"));
    }
}
